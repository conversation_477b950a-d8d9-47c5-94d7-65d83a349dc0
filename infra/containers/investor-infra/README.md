# Investor Infrastructure Service

A FastAPI + FastMCP service for interacting with PostgreSQL investor database using Prisma ORM.

## Features

- **FastAPI** web server with REST API endpoints
- **FastMCP** server for MCP (Model Context Protocol) integration
- **Prisma ORM** for type-safe database operations and migrations
- **PostgreSQL** database with structured investor data model
- **Comprehensive investor management** with contacts, investments, and documents

## Database Schema

The service manages a complete investor ecosystem:

- **Investors** - Core investor information with type classification
- **Investor Contacts** - Contact persons associated with investors
- **Investments** - Investment history and portfolio tracking
- **Investor Notes** - Internal notes and communications
- **Investor Documents** - Document management and storage

## MCP Tools

The service provides the following MCP tools using Prisma:

### Investor Operations (Prisma-based)

- `search_investors(query=None, geography=None, investor_type=None, status=None, row_limit=100)` - Advanced investor search
- `get_investor_by_phone(phone)` - Get investor by phone with full relations
- `get_investor_by_email(email)` - Get investor by email with full relations
- `get_investor_by_id(investor_id)` - Get complete investor profile
- `list_investor_types()` - List all available investor types
- `get_investor_contacts(investor_id)` - Get all contacts for an investor
- `get_investor_investments(investor_id)` - Get investment history

### Database Operations (Legacy SQL)

- `db_identity()` - Show current database identity, search_path, and version
- `list_schemas(include_system=False)` - List database schemas
- `list_tables(db_schema, name_like=None, row_limit=1000)` - List tables in a schema
- `describe_table(db_schema, table_name)` - Describe table columns
- `run_query(sql, parameters=None, row_limit=200)` - Execute read-only SQL queries

### Resources

- `investor://{investor_id}` - Get full investor profile as a resource

## Quick Start

### Option 1: Use Start Script (Recommended)

```bash
# Start everything with one command
./infra/containers/investor-infra/scripts/start-local.sh

# Stop services
./infra/containers/investor-infra/scripts/stop-local.sh
```

The start script will:

- ✅ Start PostgreSQL database
- ✅ Start FastAPI service
- ✅ Run Prisma migrations
- ✅ Generate Prisma client
- ✅ Seed database with sample data

### Option 2: Manual Setup

```bash
# Navigate to investor-infra directory
cd infra/containers/investor-infra

# Start services
docker-compose -f docker-compose.local.yaml up -d

# Setup database
export DATABASE_URL="postgresql://virgin:virgin@localhost:5433/investor_db"
export INVESTOR_DATABASE_URL="postgresql://virgin:virgin@localhost:5433/investor_db"

# Generate Prisma client
prisma generate --schema=./prisma/schema.prisma

# Run migrations
prisma migrate dev --name initial_investor_schema --schema=./prisma/schema.prisma

# Seed database
python ./scripts/seed_data.py
```

### Services Available

- **FastAPI Server**: http://localhost:8081
- **API Documentation**: http://localhost:8081/docs
- **Health Check**: http://localhost:8081/health
- **PostgreSQL**: localhost:5433 (user: virgin, password: virgin, db: investor_db)

### Environment Variables

- `DATABASE_URL` - PostgreSQL connection string for Prisma (default: `postgresql://postgres:postgres@localhost:5432/postgres`)
- `STATEMENT_TIMEOUT_MS` - SQL statement timeout in milliseconds (default: 15000)

## Database Migration

The service uses Prisma for database migrations:

### Setup Database

```bash
# Navigate to investor-infra directory
cd infra/containers/investor-infra

# Set environment variables
export DATABASE_URL="postgresql://virgin:virgin@localhost:5433/investor_db"

# Generate Prisma client
prisma generate --schema=./prisma/schema.prisma

# Run migrations
prisma migrate dev --name initial_investor_schema --schema=./prisma/schema.prisma

# Seed database with sample data
python ./scripts/seed_data.py
```

### Migration Scripts

The `scripts/migrate.py` utility provides convenient migration commands:

```bash
# Setup everything (generate client, migrate, seed)
python scripts/migrate.py setup

# Generate Prisma client only
python scripts/migrate.py generate

# Create new migration
python scripts/migrate.py migrate --name my_migration_name

# Deploy migrations (production)
python scripts/migrate.py deploy

# Reset database (development)
python scripts/migrate.py reset

# Seed database with sample data
python scripts/migrate.py seed
```

### API Endpoints

- `GET /` - Health check
- `GET /health` - Health check with database connectivity
- `GET /investors` - List investors with filters (query, geography, investor_type, status, limit)
- `GET /investors/{investor_id}` - Get investor by ID
- `GET /investors/{investor_id}/contacts` - Get investor contacts
- `GET /investors/{investor_id}/investments` - Get investor investments
- `GET /investor-types` - List all available investor types

## Deployment

### Using AWS Copilot

```bash
# Deploy as Backend Service
copilot svc deploy --name investor-infra-backend --env dev

# Deploy as Request-Driven Web Service
copilot svc deploy --name investor-infra-api --env dev
```

### Using Docker Script

```bash
# Deploy Docker image to ECR and Lambda
./scripts/deploy-investor-infra-docker.sh 183295412412.dkr.ecr.us-east-1.amazonaws.com investor-infra/investor-infra-api
```

## MCP Integration

To use this service as an MCP server with Cursor or Claude Desktop, add to your MCP configuration:

```json
{
  "servers": {
    "investor-infra": {
      "command": "uv",
      "args": [
        "run",
        "python",
        "/path/to/infra/containers/investor-infra/app/investor_infra/index.py"
      ],
      "env": {
        "POSTGRES_URL": "********************************/db?sslmode=require",
        "STATEMENT_TIMEOUT_MS": "15000"
      }
    }
  }
}
```

## Prisma Schema

The database schema is defined in `prisma/schema.prisma` and includes:

- **Investor Types**: PRIVATE_EQUITY, VENTURE_CAPITAL, HEDGE_FUND, FAMILY_OFFICE, etc.
- **Comprehensive investor data**: AUM, vintage year, geographic location, contact info
- **Related entities**: Contacts, investments, notes, documents
- **Type safety**: All operations are type-safe through Prisma Python client
