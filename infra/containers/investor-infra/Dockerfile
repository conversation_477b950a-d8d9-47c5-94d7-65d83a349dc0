FROM public.ecr.aws/docker/library/python:3.12.0-slim-bookworm

# AWS Lambda Adapter - required for streaming responses when running python code as lambda function URL
COPY --from=public.ecr.aws/awsguru/aws-lambda-adapter:0.9.1 /lambda-adapter /opt/extensions/lambda-adapter

# Change the working directory to the `app` directory
WORKDIR /code
COPY ./requirements.txt /code/requirements.txt
RUN --mount=type=cache,target=/root/.cache/pip \
    pip install --upgrade -r /code/requirements.txt

COPY ./app /code/app
COPY ./prisma /code/prisma

# Install Node.js for Prisma CLI (specific version)
RUN apt-get update && apt-get install -y nodejs npm && \
    npm install -g prisma@5.17.0 --force && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# Generate Prisma client
WORKDIR /code
RUN prisma generate --schema=./prisma/schema.prisma

# Run with uvicorn
WORKDIR /code/app
CMD exec uvicorn "investor_infra.index:app" --host 0.0.0.0 --port 8080 --timeout-keep-alive 1200
