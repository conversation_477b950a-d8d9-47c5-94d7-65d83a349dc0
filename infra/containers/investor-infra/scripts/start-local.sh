#!/bin/bash

# Start Investor Infrastructure Service Locally
# This script sets up the complete development environment

set -e

echo "🚀 Starting Investor Infrastructure Service..."

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Navigate to the investor-infra directory
cd "$(dirname "$0")/.."

echo "📁 Working directory: $(pwd)"

# Function to wait for database
wait_for_database() {
    echo "⏳ Waiting for database to be ready..."
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose -f docker-compose.local.yaml exec postgres pg_isready -U virgin -d investor_db >/dev/null 2>&1; then
            echo "✅ Database is ready!"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts: Database not ready yet..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ Database failed to start after $max_attempts attempts"
    return 1
}

# Start services
echo "🐳 Starting Docker Compose services..."
docker-compose -f docker-compose.local.yaml up -d

# Wait for database
if ! wait_for_database; then
    echo "❌ Failed to start database. Stopping services..."
    docker-compose -f docker-compose.local.yaml down
    exit 1
fi

# Setup database
echo "🗄️  Setting up database..."

# Setup development environment
echo "🔧 Setting up development environment..."
if [[ ! -d "venv" ]]; then
    echo "🐍 Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "⚡ Activating virtual environment..."
source venv/bin/activate

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip install --upgrade pip
pip install -r requirements.txt

# Check if we need to install Prisma CLI
if ! command -v prisma &> /dev/null; then
    echo "📦 Installing Prisma CLI..."
    npm install -g prisma
fi

# Setup Prisma
echo "⚙️  Setting up Prisma..."
export DATABASE_URL="postgresql://virgin:virgin@localhost:5433/investor_db"
export INVESTOR_DATABASE_URL="postgresql://virgin:virgin@localhost:5433/investor_db"

# Generate Prisma client
echo "⚙️  Generating Prisma client..."
prisma generate --schema=./prisma/schema.prisma

# Run migrations
echo "🔄 Running database migrations..."
prisma migrate dev --name initial_investor_schema --schema=./prisma/schema.prisma

# Seed database
echo "🌱 Seeding database with sample data..."
python3 ./scripts/seed_data.py

echo ""
echo "🎉 Investor Infrastructure Service is now running!"
echo ""
echo "📍 Services:"
echo "   • FastAPI Server: http://localhost:8081"
echo "   • Health Check: http://localhost:8081/health"
echo "   • API Docs: http://localhost:8081/docs"
echo "   • PostgreSQL: localhost:5433"
echo ""
echo "🔧 Management Commands:"
echo "   • View logs: docker-compose -f docker-compose.local.yaml logs -f"
echo "   • Stop services: docker-compose -f docker-compose.local.yaml down"
echo "   • Database shell: docker-compose -f docker-compose.local.yaml exec postgres psql -U virgin -d investor_db"
echo ""
echo "📚 API Endpoints:"
echo "   • GET /investors - List investors"
echo "   • GET /investors/{id} - Get investor details"
echo "   • GET /investor-types - List investor types"
echo ""
echo "🎯 MCP Integration:"
echo "   Use this service as an MCP server with your favorite client"
echo "   Command: python app/investor_infra/index.py"
echo ""
