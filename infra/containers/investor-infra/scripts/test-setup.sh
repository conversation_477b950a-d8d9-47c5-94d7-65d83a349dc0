#!/bin/bash

# Test script to verify investor-infra setup
set -e

echo "🧪 Testing Investor Infrastructure setup..."

cd "$(dirname "$0")/.."

echo "📁 Working directory: $(pwd)"

# Check required files
echo "📋 Checking required files..."

required_files=(
    "prisma/schema.prisma"
    "app/investor_infra/__init__.py"
    "app/investor_infra/index.py"
    "app/investor_infra/database.py"
    "app/investor_infra/models.py"
    "docker-compose.local.yaml"
    "Dockerfile"
    "requirements.txt"
)

for file in "${required_files[@]}"; do
    if [[ -f "$file" ]]; then
        echo "   ✅ $file"
    else
        echo "   ❌ $file (missing)"
        exit 1
    fi
done

# Check Prisma schema syntax
echo ""
echo "🔍 Checking Prisma schema syntax..."
if python3 -c "
import re
schema_content = open('prisma/schema.prisma').read()
if 'model Investor' in schema_content and 'datasource db' in schema_content and 'generator client' in schema_content:
    print('   ✅ Prisma schema looks valid')
else:
    print('   ❌ Prisma schema incomplete')
    exit(1)
"; then
    echo "   ✅ Schema validation passed"
else
    echo "   ❌ Schema validation failed"
    exit 1
fi

# Check Python imports
echo ""
echo "🐍 Checking Python module structure..."
cd app
if python3 -c "
import sys
sys.path.insert(0, '.')
try:
    import investor_infra
    print('   ✅ investor_infra module imports correctly')
except ImportError as e:
    print(f'   ❌ Import error: {e}')
    exit(1)
"; then
    echo "   ✅ Python modules OK"
else
    echo "   ❌ Python module issues"
    exit 1
fi

cd ..

echo ""
echo "🎉 Setup verification completed successfully!"
echo ""
echo "💡 Next steps:"
echo "   1. Install Docker if not already installed"
echo "   2. Run: ./scripts/start-local.sh"
echo "   3. Access service at http://localhost:8081"
echo ""
