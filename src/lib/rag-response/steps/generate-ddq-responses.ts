import type {
  CitationResponse,
  DocumentChunkType,
  SingleDDQResponse,
} from "~/lib/rag-response/steps/types";
import type { ModelParameters } from "~/views/chat/ChatModelParametersSelector";
import { tracing, TracingContext } from "~/server/api/managers/tracingManager";
import { env } from "~/env";
import {
  InvokeCommand,
  InvokeCommandInput,
  LambdaClient,
} from "@aws-sdk/client-lambda";
import {
  AnswerDataMode,
  getAnswerGenDataMode,
} from "~/utils/answer_gen_data_mode";
import { getPyrpcApiUrl } from "~/utils/url";
import { backoffDelay } from "~/server/utils/backoff";
import { type JSONResponse } from "~/lib/rag-response/steps/5b-generate-answer";

export async function generateDDQResponses(
  questionsInput: {
    questionId: string;
    citations: CitationResponse;
    contextualizedQuestion: Promise<string>;
    questionType: string;
    answerTemplate: string;
    customPrompt?: string;
    funds: string[];
    existingAnswer?: string;
  }[],
  chunks: DocumentChunkType[],
  context: { institutionName: string },
  modelParameters: ModelParameters,
  secret?: Record<string, string>,
  tracingContext?: TracingContext,
): Promise<SingleDDQResponse[]> {
  const span = tracing?.trace({
    name: "generateDDQResponses",
    tracingContext,
    input: {
      questions: questionsInput.length,
      chunks: chunks.length,
    },
  });

  const nestedTracingContext = {
    ...tracingContext,
    parentSpanId: span?.id,
  };

  const questionPayload = await Promise.all(
    questionsInput.map(async (q) => ({
      question_id: q.questionId,
      answer_template: q.answerTemplate,
      contextualized_question: await q.contextualizedQuestion,
      context,
      question_type: q.questionType,
      custom_prompt: q.customPrompt,
      existing_answer: q.existingAnswer,
      funds: q.funds.join(","),
      citations: JSON.stringify(
        q.citations.citationsWithSourceIdsAndQuotes.map(
          ({ quote, file_name, document_id }) => ({
            quote,
            file_name,
            document_id,
          }),
        ),
      ),
    })),
  );

  console.time(`generateAnswer-${questionsInput.length}`);

  const MAX_RETRIES = 3;
  let retries = 0;

  while (retries < MAX_RETRIES) {
    try {
      let responseJson: SingleDDQResponse[] = [];

      if (
        process.env.NODE_ENV === "production" ||
        process.env.EXECUTE_LAMBDAS_ON_LOCAL_DEV === "true"
      ) {
        const lambdaParams = {
          region: env.AWS_LAMBDA_REGION,
          credentials: process.env.AWS_LAMBDA_ACCESS_KEY
            ? {
                accessKeyId: env.AWS_LAMBDA_ACCESS_KEY,
                secretAccessKey: env.AWS_LAMBDA_SECRET_ACCESS_KEY,
              }
            : undefined,
        };

        // Execute Python Lambda
        const lambdaClient = new LambdaClient(lambdaParams);

        const payload = JSON.stringify({
          question_array: JSON.stringify(questionPayload),
          jumbo_chunks: JSON.stringify(chunks),
          data_mode: AnswerDataMode.JUMBO_CHUNKS,
          context,
          modelParameters,
          tracingContext: nestedTracingContext,
          secret: secret ?? env.AWS_LAMBDA_SECRET_ID,
        });
        console.log("Invoking generateDDQResponses Lambda");

        const invokeParams: InvokeCommandInput = {
          FunctionName: `generateDDQResponses-${env.LAMBDA_ENV}`,
          InvocationType: "RequestResponse",
          Payload: payload,
        };

        try {
          const command = new InvokeCommand(invokeParams);
          const response = await lambdaClient.send(command);

          console.log("generateDDQResponses Lambda response", response);

          const jsonResponse = JSON.parse(
            Buffer.from(response.Payload ?? "").toString(),
          ) as JSONResponse;

          if (jsonResponse.statusCode !== 200) {
            throw new Error(`HTTP error! status: ${jsonResponse.statusCode}`);
          }

          responseJson = jsonResponse.body;
        } catch (error) {
          console.error(
            `Error invoking generateDDQResponses-${env.LAMBDA_ENV} Lambda`,
            error,
          );
          throw error;
        }
      } else {
        console.log("Calling /pyrpc/generate-answer/generate_ddq_responses");

        const response = await fetch(
          `${getPyrpcApiUrl(secret)}/pyrpc/generate-answer/generate_ddq_responses?data_mode=${getAnswerGenDataMode()}`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              question_array: JSON.stringify(questionPayload),
              jumbo_chunks: JSON.stringify(chunks),
              data_mode: AnswerDataMode.JUMBO_CHUNKS,
              context,
              modelParameters,
              tracingContext: nestedTracingContext,
            }),
          },
        );

        if (!response.ok || response.status !== 200 || !response.body) {
          if (response.status === 422) {
            console.log(
              "422 error, check validation error.",
              await response.json(),
            );

            // return questionsInput.map((q) => ({
            //   questionId: q.questionId,
            //   answer: INSUFFICIENT_DATA_ERROR,
            //   reason: "Validation error",
            //   insufficientData: true,
            //   citations: q.citations,
            // }));
          }
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        responseJson = (await response.json()) as SingleDDQResponse[];
      }

      console.timeEnd(`generateAnswer-${questionsInput.length}`);

      // console.log(
      //   `generateAnswer-${questionsInput.length}: responseJson`,
      //   JSON.stringify(responseJson, null, 2),
      // );

      span?.update({ output: responseJson });

      return responseJson.map((r) => ({
        ...r,
        citations: questionsInput.find((q) => q.questionId === r.questionId)
          ?.citations ?? {
          citationsWithSourceIdsAndQuotes: [],
        },
      }));
    } catch (error) {
      span?.update({ level: "ERROR", statusMessage: (error as Error).message });
      console.error(`Error generating DDQ responses`, error);

      retries++;
      await backoffDelay(retries);
    } finally {
      if (retries >= MAX_RETRIES) {
        tracing?.end();
      }
    }
  }

  tracing?.end();

  return [];
  // return questionsInput.map((q) => ({
  //   questionId: q.questionId,
  //   answer: INSUFFICIENT_DATA_ERROR,
  //   reason: "Unable to generate DDQ responses",
  //   citations: q.citations,
  //   insufficientData: true,
  // }));
}
