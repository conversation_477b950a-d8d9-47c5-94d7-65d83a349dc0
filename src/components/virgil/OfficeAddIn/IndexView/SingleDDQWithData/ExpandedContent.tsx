import { memo } from "react";
import React from "react";
import { DDQuestionWithIndexAndFeedback } from "~/server/api/managers/questionManager";
import useActionButtons from "./useActionButtons";
import { ResponseDetails } from "../../../Response/ResponseDetails";
import parseQuestionData from "./parseQuestionData";
import { cn } from "~/v2/lib/utils";
import { Checkbox } from "~/v2/components/ui/Checkbox";
import { Typography } from "~/v2/components";
import { selectShowStandardResponses } from "~/lib/features/addInSelectors";
import { useSelector } from "react-redux";

interface ExpandedContentProps {
  question: DDQuestionWithIndexAndFeedback;
  isChecked: boolean;
  setIsChecked: (questionId: string) => void;
}

export const ExpandedContent = memo(
  ({
    question,
    isChecked,
    setIsChecked,
  }: ExpandedContentProps) => {
    const { ActionButtonsMemo, isGenerating } = useActionButtons({
      question,
      view: "index",
    });
    const showStandardResponses = useSelector(selectShowStandardResponses);
    const {
      questionText,
      responseReason,
      responseStatus,
      responseText,
      answerGenerationType,
      insufficientData,
      isAnswered,
      questionId,
      topSimilarityScore,
      responseSource,
    } = parseQuestionData(question, showStandardResponses);

    return (
      <div
        data-testid={`expanded-${question.id}`}
        data-question-id={question.id}
        className={cn(
          "w-full",
          "last:border-b-0",
          "first:border-t-1 first:border-t-[var(--border)]",
          "border-b border-b-[1px] border-b-[var(--border)]",
          "relative",
          "bg-[var(--colors-component-selected)]",
          "active:bg-[var(--colors-component-selected)]",
          "flex",
          "p-[var(--spacing-2)]",
          "gap-x-[var(--spacing-2)]",
          "relative",
          "before:content-['']",
          "before:absolute",
          "before:top-0",
          "before:left-0",
          "before:w-[var(--spacing-1)]",
          "before:h-full",
          "before:bg-[var(--primary)]",
        )}
      >
        <Checkbox
          checked={isChecked}
          onCheckedChange={() => {
            setIsChecked(questionId);
          }}
          onClick={(e) => e.stopPropagation()}
        />
        <div className="flex-1">
          <Typography variant="boldBody">{questionText}</Typography>
          <ResponseDetails
            responseReason={responseReason}
            responseStatus={responseStatus}
            responseText={responseText}
            answerGenerationType={answerGenerationType}
            insufficientData={insufficientData}
            isAnswered={isAnswered}
            isGenerating={isGenerating}
            maxHeight={300}
            displayAnsweredStatus={true}
            topSimilarityScore={topSimilarityScore}
            showStandardResponses={showStandardResponses}
            responseSource={responseSource ?? ""}
          />
          <div className="mt-2">{ActionButtonsMemo}</div>
        </div>
      </div>
    );
  },
);
