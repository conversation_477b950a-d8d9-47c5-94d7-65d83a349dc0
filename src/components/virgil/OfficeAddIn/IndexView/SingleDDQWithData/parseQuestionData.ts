import { AnswerGenerationType, QuestionStatusType } from "@prisma/client";
import { DDQuestionWithFeedbackAndSimilarQuestions } from "~/server/api/managers/questionManager";

export default function parseQuestionData(
  question: DDQuestionWithFeedbackAndSimilarQuestions,
  showStandardResponses = false,
) {
  const topSimilarQuestion = question.similarQuestions?.sort(
    (a, b) => (b.similarity ?? 0) - (a.similarity ?? 0),
  )[0];

  const topSimilarityScore = topSimilarQuestion?.similarity;

  const { text: questionText, id: questionId, response } = question;

  const shouldUseStandardResponse = showStandardResponses && topSimilarQuestion;

  const responseContents = shouldUseStandardResponse
    ? topSimilarQuestion?.responseContents
    : response?.responseContents;

  // Conditionally override
  const answerGenerationType = shouldUseStandardResponse
    ? AnswerGenerationType.EXTRACTED
    : responseContents?.[0]?.answerGenerationType;
  const responseStatus = shouldUseStandardResponse
    ? topSimilarQuestion?.responseContents?.[0]?.status
    : response?.status;
  const responseCanonicalId = shouldUseStandardResponse
    ? (topSimilarQuestion.responseId ?? "")
    : (response?.id ?? "");
  const responseId = shouldUseStandardResponse
    ? (responseContents?.[0]?.id ?? "")
    : (response?.responseContents?.[0]?.id ?? "");
  const responseSource = shouldUseStandardResponse
    ? topSimilarQuestion?.documentName
    : null;

  const responseContent = responseContents?.[0]?.content as
    | { reason?: string; text?: string; insufficientData?: boolean }
    | undefined;
  const responseReason = responseContent?.reason ?? "";
  const responseText = responseContent?.text ?? "";

  const documentId = response?.documents[0]?.documentId ?? "";
  const insufficientData = responseContent?.insufficientData ?? false;

  return {
    questionText,
    questionId,
    answerGenerationType,
    responseReason,
    responseStatus,
    responseText,
    responseId,
    responseCanonicalId,
    documentId,
    insufficientData,
    questionStatus: question.status,
    isAnswered: question.status === QuestionStatusType.ANSWERED,
    topSimilarityScore,
    responseSource,
  };
}
